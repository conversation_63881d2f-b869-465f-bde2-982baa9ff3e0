# Syncthing Multi-Device Setup

A clean, deterministic Syncthing configuration system with static device IDs, 1Password secret management, and source-controlled configs.

## Architecture Overview

- **Static Device IDs**: Deterministic device IDs generated from seed strings, stored in source control
- **Auto-Generated Certificates**: Each device generates its own certificates on first run (more secure)
- **1Password Integration**: API keys stored in 1Password with `op://` references
- **Pi as Central Hub**: Raspberry Pi configured as introducer for easy device management
- **Consistent Paths**: All devices use `--home ~/.config/syncthing` for predictable behavior

## Device Configuration

| Device | Device ID | Folder Path | Role |
|--------|-----------|-------------|------|
| MBP | `GUFL7D6-FTJUTCC-Z5YVPNK-HCLYZ7D-PXFDL2U-AWHF2JV-3RF26XQ-V2Q====` | `~/Notes` (symlink to git repo) | Client |
| Pi | `W6KJSD5-T5XGQJY-L37WXWZ-BKXCXNH-2NS4NOR-ZPMSW53-FPY7H7Q-TGQ====` | `~/Synced/Notes` | Hub/Introducer |
| iPhone | `4IC2HCY-ARNMNZQ-E335BPJ-MZPCYFA-O7IUXSF-M6UIYRY-MKDFICZ-UDBIRAV` | Manual setup required | Client |

## Prerequisites

### 1Password Setup
Create API key items in 1Password:
```bash
# In MAC_ENVS vault
op item create --vault='MAC_ENVS' --category='API Credential' --title='SYNCTHING_API_KEY' 'credential[password]=<your-api-key>'

# In PI_ENVS vault  
op item create --vault='PI_ENVS' --category='API Credential' --title='SYNCTHING_API_KEY' 'credential[password]=<your-api-key>'
```

### Dependencies
- `op` CLI tool (1Password)
- `ssh` access to Pi
- Syncthing installed on both devices

## File Structure

```
syncthing/
├── configs/
│   ├── mbp-config.xml          # MBP configuration with op:// references
│   └── pi-config.xml           # Pi configuration with op:// references
├── deploy-mbp.sh               # MBP deployment script
├── deploy-pi.sh                # Pi deployment script
├── deploy-all.sh               # Master deployment script
├── generate_device_ids.py      # Device ID generation (for reference)
└── README.md                   # This file
```

## Key Design Decisions

### Static Device IDs
- Generated deterministically from seed strings
- Allows predictable device pairing across reinstalls
- Stored in source control for team consistency
- No need to exchange device IDs manually

### Certificate Strategy
- **NOT stored in source control or 1Password**
- Each device generates its own certificates on first run
- More secure than distributing certificates
- Syncthing handles certificate exchange automatically

### 1Password Integration
- API keys stored as `op://VAULT/SYNCTHING_API_KEY/credential`
- Secrets injected at deployment time using `op inject`
- No secrets in source control
- Different vaults for different environments (MAC_ENVS, PI_ENVS)

### Pi as Hub
- Pi configured as `introducer="true"`
- New devices added to Pi will be automatically shared with other devices
- Simplifies iPhone setup (add to Pi, Pi introduces to MBP)

## Deployment

### Quick Start
```bash
# Deploy to both devices
./deploy-all.sh

# Or deploy individually
./deploy-mbp.sh
./deploy-pi.sh
```

### Manual Steps

#### MBP Deployment
```bash
./deploy-mbp.sh
```
- Stops existing Syncthing
- Injects 1Password API key into config
- Copies config to `~/.config/syncthing/`
- Creates `~/Notes` symlink to git repo
- Starts Syncthing with `--home` option

#### Pi Deployment
```bash
./deploy-pi.sh [hostname]
```
- Uses SSH to deploy to Pi (default hostname: `pi`)
- Injects 1Password API key into config
- Updates systemd service with `--home` option
- Creates `~/Synced/Notes` directory

## iPhone Setup

⚠️ **Manual Setup Required**

The iPhone device ID (`4IC2HCY-ARNMNZQ-E335BPJ-MZPCYFA-O7IUXSF-M6UIYRY-MKDFICZ-UDBIRAV`) is pre-configured in both MBP and Pi configs, but iPhone setup must be done manually:

1. Install Syncthing app on iPhone
2. Add Pi as a device using Pi's device ID
3. Accept folder sharing for "Notes"

**Important**: If the iPhone app is reinstalled, the device ID will change and configs must be updated.

## Folder Sync Strategy

- **MBP**: `~/Notes` → symlink to `~/Development/personal/notes/data` (git repo)
- **Pi**: `~/Synced/Notes` → local storage
- **iPhone**: App-managed location

The git repository remains untouched by Syncthing - only the `data` folder is synced.

## Troubleshooting

### Check Service Status
```bash
# MBP
pgrep -f "syncthing.*--home"
tail -f ~/.config/syncthing/syncthing.log

# Pi
ssh pi "systemctl --user status syncthing"
ssh pi "journalctl --user -u syncthing -f"
```

### Web UIs
- MBP: https://localhost:8384
- Pi: https://pi:8384

### Common Issues

1. **1Password CLI not found**: Scripts will copy configs without secret injection
2. **SSH key issues**: Ensure SSH key authentication to Pi
3. **Certificate errors**: Delete `~/.config/syncthing/cert.pem` and `key.pem`, restart Syncthing
4. **Sync not working**: Check device connections in web UI

## Security Considerations

- Device certificates are unique per device (auto-generated)
- API keys stored securely in 1Password
- No secrets in source control
- SSH used for Pi deployment (ensure key-based auth)

## Maintenance

### Adding New Devices
1. Generate new device ID using `generate_device_ids.py`
2. Add device to both config files
3. Redeploy configs
4. Manual setup on new device

### Updating Configs
1. Edit `configs/*.xml` files
2. Run deployment scripts
3. Restart Syncthing services

### iPhone Reinstall
1. Note new device ID from iPhone app
2. Update both config files with new device ID
3. Redeploy configs
4. Re-add Pi on iPhone
