#!/usr/bin/env bash
set -euo pipefail

echo "Deploying Syncthing configuration to MBP..."

# Stop Syncthing if running
echo "Stopping Syncthing..."
pkill -f syncthing || true
sleep 2

# Create config directory
mkdir -p ~/.config/syncthing

# Backup existing config if it exists
if [[ -f ~/.config/syncthing/config.xml ]]; then
    echo "Backing up existing config..."
    cp ~/.config/syncthing/config.xml ~/.config/syncthing/config.xml.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy new config
echo "Deploying new config..."
cp configs/mbp-config.xml ~/.config/syncthing/config.xml

# Copy certificates
echo "Deploying certificates..."
cp certs/mbp-cert.pem ~/.config/syncthing/cert.pem
cp certs/mbp-key.pem ~/.config/syncthing/key.pem

# Set proper permissions
chmod 600 ~/.config/syncthing/key.pem
chmod 644 ~/.config/syncthing/cert.pem
chmod 644 ~/.config/syncthing/config.xml

# Create the Notes symlink if it doesn't exist
if [[ ! -L ~/Notes ]]; then
    echo "Creating ~/Notes symlink..."
    ln -sf ~/Development/personal/notes/data ~/Notes
fi

# Start Syncthing with --home option
echo "Starting Syncthing with --home option..."
nohup syncthing --home ~/.config/syncthing --no-browser --no-restart > ~/.config/syncthing/syncthing.log 2>&1 &

# Wait a moment for startup
sleep 3

# Check if it's running
if pgrep -f "syncthing.*--home" > /dev/null; then
    echo "✅ Syncthing started successfully!"
    echo "Web UI: https://localhost:8384"
    echo "Log file: ~/.config/syncthing/syncthing.log"
else
    echo "❌ Failed to start Syncthing"
    echo "Check log: ~/.config/syncthing/syncthing.log"
    exit 1
fi

echo "MBP deployment complete!"
