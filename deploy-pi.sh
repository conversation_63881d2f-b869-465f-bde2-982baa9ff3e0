#!/usr/bin/env bash
set -euo pipefail

PI_HOST="${1:-pi}"

echo "Deploying Syncthing configuration to Pi ($PI_HOST)..."

# Create config directory on Pi
echo "Creating config directory on Pi..."
ssh "$PI_HOST" "mkdir -p ~/.config/syncthing"

# Backup existing config if it exists
echo "Backing up existing config on Pi..."
ssh "$PI_HOST" "if [[ -f ~/.config/syncthing/config.xml ]]; then cp ~/.config/syncthing/config.xml ~/.config/syncthing/config.xml.backup.\$(date +%Y%m%d_%H%M%S); fi"

# Stop Syncthing service on Pi
echo "Stopping Syncthing service on Pi..."
ssh "$PI_HOST" "systemctl --user stop syncthing || true"
sleep 2

# Process and copy config to <PERSON> with 1Password injection
echo "Copying config to <PERSON> with 1Password API key injection..."
if command -v op >/dev/null 2>&1; then
    # Create temp file with injected secrets
    TEMP_CONFIG=$(mktemp)
    op inject -i configs/pi-config.xml -o "$TEMP_CONFIG"
    scp "$TEMP_CONFIG" "$PI_HOST":~/.config/syncthing/config.xml
    rm "$TEMP_CONFIG"
    echo "✅ API key injected from 1Password"
else
    echo "⚠️  1Password CLI not found, copying config as-is"
    scp configs/pi-config.xml "$PI_HOST":~/.config/syncthing/config.xml
fi

# Set proper permissions on Pi (certificates will be auto-generated by Syncthing)
echo "Setting permissions on Pi..."
ssh "$PI_HOST" "chmod 644 ~/.config/syncthing/config.xml"

# Create sync directory on Pi
echo "Creating sync directory on Pi..."
ssh "$PI_HOST" "mkdir -p ~/Synced/Notes"

# Update systemd service to use --home option
echo "Updating systemd service on Pi..."
ssh "$PI_HOST" "cat > ~/.config/systemd/user/syncthing.service << 'EOF'
[Unit]
Description=Syncthing - Open Source Continuous File Synchronization
Documentation=man:syncthing(1)
After=network.target

[Service]
ExecStart=/usr/local/bin/syncthing --home ~/.config/syncthing --no-browser --no-restart --logflags=0
Restart=on-failure
RestartSec=5
SuccessExitStatus=3 4
RestartForceExitStatus=3 4

[Install]
WantedBy=default.target
EOF"

# Reload systemd and start service
echo "Reloading systemd and starting service on Pi..."
ssh "$PI_HOST" "systemctl --user daemon-reload"
ssh "$PI_HOST" "systemctl --user enable syncthing"
ssh "$PI_HOST" "systemctl --user start syncthing"

# Wait for startup
sleep 5

# Check if service is running
echo "Checking service status on Pi..."
if ssh "$PI_HOST" "systemctl --user is-active syncthing" | grep -q "active"; then
    echo "✅ Syncthing service started successfully on Pi!"
    echo "Web UI: https://$PI_HOST:8384"
else
    echo "❌ Failed to start Syncthing service on Pi"
    ssh "$PI_HOST" "systemctl --user status syncthing"
    exit 1
fi

echo "Pi deployment complete!"
