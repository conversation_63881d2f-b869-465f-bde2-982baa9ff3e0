#!/usr/bin/env python3
"""
Generate deterministic Syncthing device IDs.
This ensures consistent device IDs across deployments.
"""

import hashlib
import base64
import os

def generate_device_id(seed_string):
    """Generate a deterministic Syncthing device ID from a seed string."""
    # Create a deterministic hash from the seed
    hash_obj = hashlib.sha256(seed_string.encode('utf-8'))
    hash_bytes = hash_obj.digest()

    # Syncthing device IDs are base32-encoded with specific formatting
    # Take first 32 bytes and encode as base32
    device_bytes = hash_bytes[:32]
    device_id = base64.b32encode(device_bytes).decode('ascii')

    # Format as Syncthing device ID (7 chars, dash, repeat)
    formatted_id = '-'.join([device_id[i:i+7] for i in range(0, len(device_id), 7)])
    return formatted_id[:63]  # Syncthing device IDs are 63 chars

def generate_certificate(device_name, device_id):
    """Generate a certificate and private key for a Syncthing device."""
    from cryptography import x509
    from cryptography.x509.oid import NameOID
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    import datetime
    import ipaddress

    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )

    # Create certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COMMON_NAME, f"syncthing-{device_name}"),
    ])

    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=3650)  # 10 years
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("localhost"),
            x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
        ]),
        critical=False,
    ).sign(private_key, hashes.SHA256())

    return private_key, cert

def main():
    devices = {
        'mbp': 'chris-macbook-pro-syncthing-2025',
        'pi': 'chris-raspberry-pi-syncthing-2025',
        'iphone': 'chris-iphone-syncthing-2025'
    }

    print("Generating deterministic Syncthing device IDs and certificates...")
    print("=" * 60)

    os.makedirs('certs', exist_ok=True)
    os.makedirs('configs', exist_ok=True)

    device_info = {}

    for device_name, seed in devices.items():
        print(f"\n{device_name.upper()}:")

        # Generate device ID
        device_id = generate_device_id(seed)
        print(f"Device ID: {device_id}")

        # Generate certificate
        private_key, cert = generate_certificate(device_name, device_id)

        # Save certificate and key
        cert_path = f"certs/{device_name}-cert.pem"
        key_path = f"certs/{device_name}-key.pem"

        from cryptography.hazmat.primitives import serialization

        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))

        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        print(f"Certificate: {cert_path}")
        print(f"Private Key: {key_path}")

        device_info[device_name] = {
            'device_id': device_id,
            'cert_path': cert_path,
            'key_path': key_path
        }
    
    # Generate API keys (deterministic)
    print(f"\nAPI KEYS:")
    for device_name in devices.keys():
        api_seed = f"{devices[device_name]}-api-key"
        api_hash = hashlib.sha256(api_seed.encode('utf-8')).hexdigest()[:32]
        print(f"{device_name.upper()}_API_KEY: {api_hash}")
        device_info[device_name]['api_key'] = api_hash
    
    print(f"\n1Password Storage Commands:")
    print("=" * 40)
    for device_name, info in device_info.items():
        print(f"\n# {device_name.upper()}")
        print(f"op item create --category='Secure Note' --title='Syncthing {device_name.upper()}' \\")
        print(f"  'device_id[text]={info['device_id']}' \\")
        print(f"  'api_key[password]={info['api_key']}' \\")
        print(f"  'certificate[text]=$(cat {info['cert_path']})' \\")
        print(f"  'private_key[password]=$(cat {info['key_path']})'")

if __name__ == "__main__":
    main()
