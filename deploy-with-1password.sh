#!/usr/bin/env bash
set -euo pipefail

echo "🔐 Deploying Syncthing with 1Password Integration"
echo "================================================="

# Function to inject 1Password secrets into config
inject_secrets() {
    local config_file="$1"
    local device_type="$2"
    
    echo "Injecting 1Password secrets for $device_type..."
    
    # Replace API key with 1Password reference
    if command -v op >/dev/null 2>&1; then
        # Use op CLI to get the actual values
        local api_key
        api_key=$(op item get "Syncthing $device_type" --field api_key 2>/dev/null || echo "")
        
        if [[ -n "$api_key" ]]; then
            sed -i.bak "s/<apikey>.*<\/apikey>/<apikey>$api_key<\/apikey>/" "$config_file"
            echo "✅ API key injected for $device_type"
        else
            echo "⚠️  Could not retrieve API key from 1Password for $device_type"
            echo "   Make sure the item 'Syncthing $device_type' exists in 1Password"
        fi
    else
        echo "⚠️  1Password CLI not found. Using static API keys."
    fi
}

# Function to get certificates from 1Password
get_certificates_from_1password() {
    local device_type="$1"
    local cert_dir="$2"
    
    if command -v op >/dev/null 2>&1; then
        echo "Getting certificates from 1Password for $device_type..."
        
        # Get certificate
        if op item get "Syncthing $device_type" --field certificate > "$cert_dir/cert.pem" 2>/dev/null; then
            echo "✅ Certificate retrieved for $device_type"
        else
            echo "⚠️  Using local certificate file for $device_type"
            cp "certs/${device_type,,}-cert.pem" "$cert_dir/cert.pem"
        fi
        
        # Get private key
        if op item get "Syncthing $device_type" --field private_key > "$cert_dir/key.pem" 2>/dev/null; then
            echo "✅ Private key retrieved for $device_type"
            chmod 600 "$cert_dir/key.pem"
        else
            echo "⚠️  Using local private key file for $device_type"
            cp "certs/${device_type,,}-key.pem" "$cert_dir/key.pem"
            chmod 600 "$cert_dir/key.pem"
        fi
    else
        echo "⚠️  1Password CLI not found. Using local certificate files."
        cp "certs/${device_type,,}-cert.pem" "$cert_dir/cert.pem"
        cp "certs/${device_type,,}-key.pem" "$cert_dir/key.pem"
        chmod 600 "$cert_dir/key.pem"
    fi
}

# Deploy to MBP
echo ""
echo "📱 Deploying to MBP with 1Password integration..."
echo "================================================"

# Stop Syncthing if running
pkill -f syncthing || true
sleep 2

# Create config directory
mkdir -p ~/.config/syncthing

# Backup existing config
if [[ -f ~/.config/syncthing/config.xml ]]; then
    cp ~/.config/syncthing/config.xml ~/.config/syncthing/config.xml.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy and process config
cp configs/mbp-config.xml ~/.config/syncthing/config.xml
inject_secrets ~/.config/syncthing/config.xml "MBP"

# Get certificates from 1Password
get_certificates_from_1password "MBP" ~/.config/syncthing

# Set permissions
chmod 644 ~/.config/syncthing/config.xml ~/.config/syncthing/cert.pem

# Create Notes symlink
if [[ ! -L ~/Notes ]]; then
    ln -sf ~/Development/personal/notes/data ~/Notes
fi

# Start Syncthing
nohup syncthing --home ~/.config/syncthing --no-browser --no-restart > ~/.config/syncthing/syncthing.log 2>&1 &
sleep 3

if pgrep -f "syncthing.*--home" > /dev/null; then
    echo "✅ MBP deployment successful!"
else
    echo "❌ MBP deployment failed"
    exit 1
fi

# Deploy to Pi
echo ""
echo "🥧 Deploying to Pi with 1Password integration..."
echo "==============================================="

PI_HOST="${1:-pi}"

# Create temp directory for Pi config
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Prepare Pi config with secrets
cp configs/pi-config.xml "$TEMP_DIR/config.xml"
inject_secrets "$TEMP_DIR/config.xml" "PI"

# Get Pi certificates
mkdir -p "$TEMP_DIR/certs"
get_certificates_from_1password "PI" "$TEMP_DIR/certs"

# Deploy to Pi
ssh "$PI_HOST" "mkdir -p ~/.config/syncthing"
ssh "$PI_HOST" "systemctl --user stop syncthing || true"
sleep 2

scp "$TEMP_DIR/config.xml" "$PI_HOST":~/.config/syncthing/config.xml
scp "$TEMP_DIR/certs/cert.pem" "$PI_HOST":~/.config/syncthing/cert.pem
scp "$TEMP_DIR/certs/key.pem" "$PI_HOST":~/.config/syncthing/key.pem

ssh "$PI_HOST" "chmod 644 ~/.config/syncthing/config.xml ~/.config/syncthing/cert.pem"
ssh "$PI_HOST" "chmod 600 ~/.config/syncthing/key.pem"
ssh "$PI_HOST" "mkdir -p ~/Synced/Notes"

# Update systemd service
ssh "$PI_HOST" "cat > ~/.config/systemd/user/syncthing.service << 'EOF'
[Unit]
Description=Syncthing - Open Source Continuous File Synchronization
After=network.target

[Service]
ExecStart=/usr/local/bin/syncthing --home ~/.config/syncthing --no-browser --no-restart --logflags=0
Restart=on-failure
RestartSec=5

[Install]
WantedBy=default.target
EOF"

ssh "$PI_HOST" "systemctl --user daemon-reload && systemctl --user enable syncthing && systemctl --user start syncthing"
sleep 5

if ssh "$PI_HOST" "systemctl --user is-active syncthing" | grep -q "active"; then
    echo "✅ Pi deployment successful!"
else
    echo "❌ Pi deployment failed"
    exit 1
fi

echo ""
echo "🎉 1Password-integrated deployment complete!"
echo "Web UIs:"
echo "  MBP: https://localhost:8384"
echo "  Pi:  https://$PI_HOST:8384"
